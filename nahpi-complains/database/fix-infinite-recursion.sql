-- Fix Infinite Recursion in RLS Policies
-- This completely removes the problematic policies and creates simpler ones

-- Drop ALL existing policies to start completely fresh
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Users can insert own profile" ON users;
DROP POLICY IF EXISTS "Admins can view all users" ON users;
DROP POLICY IF EXISTS "Admins can update all users" ON users;
DROP POLICY IF EXISTS "Admins can insert users" ON users;
DROP POLICY IF EXISTS "Students can view own data" ON students;
DROP POLICY IF EXISTS "Students can update own data" ON students;
DROP POLICY IF EXISTS "Students can insert own data" ON students;
DROP POLICY IF EXISTS "Admins can view all students" ON students;
DROP POLICY IF EXISTS "Officers can view department students" ON students;
DROP POLICY IF EXISTS "Admins can view own data" ON admins;
DROP POLICY IF EXISTS "Ad<PERSON> can insert own data" ON admins;
DROP POLICY IF EXISTS "Department officers can view own data" ON department_officers;
DROP POLICY IF EXISTS "Department officers can insert own data" ON department_officers;
DROP POLICY IF EXISTS "Ad<PERSON> can view all department_officers" ON department_officers;
DROP POLICY IF EXISTS "Everyone can view departments" ON departments;
DROP POLICY IF EXISTS "Students can view own complaints" ON complaints;
DROP POLICY IF EXISTS "Students can create complaints" ON complaints;
DROP POLICY IF EXISTS "Officers can view department complaints" ON complaints;
DROP POLICY IF EXISTS "Admins can view all complaints" ON complaints;
DROP POLICY IF EXISTS "Users can view complaint attachments" ON complaint_attachments;
DROP POLICY IF EXISTS "Users can view complaint responses" ON complaint_responses;
DROP POLICY IF EXISTS "Users can view own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update own notifications" ON notifications;

-- Temporarily disable RLS on all tables
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE students DISABLE ROW LEVEL SECURITY;
ALTER TABLE admins DISABLE ROW LEVEL SECURITY;
ALTER TABLE department_officers DISABLE ROW LEVEL SECURITY;
ALTER TABLE departments DISABLE ROW LEVEL SECURITY;
ALTER TABLE complaints DISABLE ROW LEVEL SECURITY;
ALTER TABLE complaint_attachments DISABLE ROW LEVEL SECURITY;
ALTER TABLE complaint_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE complaint_feedback DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- Insert departments first (they should have been created by the schema)
INSERT INTO departments (name, code, description) VALUES
('Centre for Cybersecurity and Mathematical Cryptology', 'CMC', 'Department focusing on cybersecurity and cryptology'),
('Chemical and Biological Engineering', 'CBE', 'Department of chemical and biological engineering'),
('Civil Engineering and Architecture', 'CVL', 'Department of civil engineering and architecture'),
('Computer Engineering', 'COM', 'Department of computer engineering'),
('Electrical and Electronics Engineering', 'EEEE', 'Department of electrical and electronics engineering'),
('Mechanical and Industrial Engineering', 'MEC', 'Department of mechanical and industrial engineering'),
('Mining and Mineral Engineering', 'MIN', 'Department of mining and mineral engineering'),
('Petroleum Engineering', 'PET', 'Department of petroleum engineering')
ON CONFLICT (code) DO NOTHING;

-- Re-enable RLS only on essential tables for now
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;

-- Create SIMPLE policies without recursion
-- Users table - allow users to see their own data and allow inserts
CREATE POLICY "Users can manage own profile" ON users 
FOR ALL USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Students table - allow students to see their own data and allow inserts  
CREATE POLICY "Students can manage own data" ON students 
FOR ALL USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Departments table - allow everyone to read (needed for dropdowns)
CREATE POLICY "Everyone can view departments" ON departments 
FOR SELECT TO authenticated USING (true);

-- For now, disable RLS on admin and department_officers tables to avoid recursion
ALTER TABLE admins DISABLE ROW LEVEL SECURITY;
ALTER TABLE department_officers DISABLE ROW LEVEL SECURITY;

-- Test query
SELECT 'RLS policies fixed - recursion removed' as status;

-- Verify departments were inserted
SELECT 'Departments:' as info, count(*) as count FROM departments;
SELECT name, code FROM departments ORDER BY code;
