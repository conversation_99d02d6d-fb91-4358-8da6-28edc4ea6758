{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key"}, "build": {"env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key"}}, "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "regions": ["iad1"], "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}]}