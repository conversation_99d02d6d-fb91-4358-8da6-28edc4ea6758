I want to build a Use this color #08387F and white then you can add any other color which will blend (but very very minimal use). Make sure you make the interfaces to be responsive (So that the website will look good on all like screens like desktop and mobile apps). First of all, Let's do the frontend with reactjs and tailwind css. Make sure each user has their own login interface and also make sure you implement the front end based on the user and system requirements in the document 'Technical & Tasks.txt'. You are free to give suggestions on how to improve the requirements and also how to implement the system. Make sure the front end is completely functional then we can move to the backend



project name: nahpi
database url: https://qbxgswcslywltbuoqnbv.supabase.co
anon public : eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieGdzd2NzbHl3bHRidW9xbmJ2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5Njc3MjMsImV4cCI6MjA2NzU0MzcyM30.2pNYsPgki4BRmdAahh7-a8i8xAnfz38cTRPe2FoHO4Q

service role secret: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFieGdzd2NzbHl3bHRidW9xbmJ2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTk2NzcyMywiZXhwIjoyMDY3NTQzNzIzfQ.OU42jvoZJN24Vr5pIHaOMPqjyl6w8NuL3gedOKHoHFc


Matricule format
'UBa<last_two_digits_of_year><any_capital_letter><four_digits>' for example 'UBa25T1000' 

Would you like me to proceed with implementing the complaint submission and management features, or would you prefer to test the authentication system first?


Logout functionality
Now lets work on the student portal and make it superd, Make sure there is no page of the student portal using mock data, and also make sure you update or delete the tabs page to represent or to be the pages which have actural database data. Remove any useless page or duplicate page. Also implement the logout functionality for the student page. And a student is unable to submit a complaint with a file attached, looks like we need to look for a proper way to handle this. 


Now let's work on the department officer portal and make it super awesome. 
-Transform all the element/any mock data on the dashboard to be gotten from the database
-Remove the take action button from the complain cards in the department complaints tab.
-In the department complaints tab, make sure I am actually able to download/export all the complaints (Bulk action) in csv or excel format. But for exporting individual complaints, I should be able to download them in pdf format.
-When I view complain details, let the status update work and the respond message should only be obligatory when I change the status to 'resolved' or 'rejected' and let the message get to the student as a notification (This is how it should be each time a complaints is either rejcted or resolved). The status updates I perform on a complain should be reflect everywhere it is suppose to reflect in the system.
-Do not forget to implement the logout functionality
-In the settings tab, let the department officer be able to edit his/her profile and make all the other functionalities there actually functional. 
-The communication(/department/communications) tab should fully be transformed/renamed into department students and should display all the students on the platform who are in that department. /department/communications
-Remove all useless stuffs
-Add any other modification you think is neccessary for the department portal of this kind of system